syntax = "proto3";

package workwx.v1;

import "gnostic/openapi/v3/annotations.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";

// 企业微信回调消息服务
service WorkwxService {

  // 企微登录
  rpc LoginWithWorkWechat(LoginWithWorkWechatRequest) returns (LoginResponse) {
    option (google.api.http) = {
      post: "/workwx/v1/loginWithWorkWechat"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      tags: [
        "系统管理",
        "用户管理"
      ]
    };
  }

  rpc GetWorkWechatLoginUrl(WorkWechatLoginUrlRequest) returns (WorkWechatLoginUrlResponse) {
    option (google.api.http) = {get: "/workwx/v1/workWechatLoginUrl"};

    option (gnostic.openapi.v3.operation) = {
      tags: [
        "系统管理",
        "用户管理"
      ]
    };
  }

  // 绑定用户企业微信
  rpc BindWxwork(BindWxworkRequest) returns (LoginResponse) {
    option (google.api.http) = {
      post: "/workwx/v1/bindWxwork"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      tags: [
        "系统管理",
        "用户管理"
      ]
    };
  }

  // 解绑用户企业微信
  rpc UnbindWxwork(UnBindWxworkRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/workwx/v1/unbindWxwork"
      body: "*"
    };

    option (gnostic.openapi.v3.operation) = {
      tags: [
        "系统管理",
        "用户管理"
      ]
    };
  }
  

  // URL验证接口
  rpc VerifyURL(VerifyURLRequest) returns (VerifyURLResponse) {
    option (google.api.http) = {
      get: "/workwx/v1/callback"
    };
    option (gnostic.openapi.v3.operation) = {
      description: "验证企业微信回调URL的有效性"
      tags: ["企业微信回调"]
    };
  }

  // 接收消息接口
  rpc ReceiveMessage(ReceiveMessageRequest) returns (ReceiveMessageResponse) {
    option (google.api.http) = {
      post: "/workwx/v1/callback"
      body: "*"
    };
    option (gnostic.openapi.v3.operation) = {
      description: "接收企业微信推送的消息和事件"
      tags: ["企业微信回调"]
    };
  }

  // 获取企业微信服务器IP段
  rpc GetCallbackIP(google.protobuf.Empty) returns (GetCallbackIPResponse) {
    option (google.api.http) = {
      get: "/workwx/v1/callback/ip"
    };
    option (gnostic.openapi.v3.operation) = {
      description: "获取企业微信回调服务器的IP段列表"
      tags: ["企业微信回调"]
    };
  }

  rpc RegisterInfo(TicketRequest) returns (RegisterInfoResponse) {
    option (google.api.http) = {get: "/workwx/v1/registerInfo/{code}"};
    option (gnostic.openapi.v3.operation) = {
      tags: [
        "系统管理",
        "用户管理"
      ]
    };
  }

  rpc JsSDKSignature(JsSDKSignatureRequest) returns (JsSDKSignatureResponse) {
    option (google.api.http) = {get: "/workwx/v1/jsSDKSignature1/{code}"};
    option (gnostic.openapi.v3.operation) = {
      tags: [
        "系统管理",
        "用户管理"
      ]
    };
  }

  rpc JsSDKConfigSignature(JsSDKSignatureRequest) returns (JsSDKSignatureResponse) {
    option (google.api.http) = {get: "/workwx/v1/jsSDKConfigSignature1/{code}"};
    option (gnostic.openapi.v3.operation) = {
      tags: [
        "系统管理",
        "用户管理"
      ]
    };
  }


  rpc CorpJsApiTicket(TicketRequest) returns (JsApiTicketResponse) {
    option (google.api.http) = {get: "/workwx/v1/coprJsApiTicket/{code}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "审批",
        "审批模版"
      ]
    };
  }

  rpc AgentJsApiTicket(TicketRequest) returns (JsApiTicketResponse) {
    option (google.api.http) = {get: "/workwx/v1/agentJsApiTicket/{code}"};

    option (gnostic.openapi.v3.operation) = {
      security: [
        {
          additional_properties: [
            {
              name: "OAuth2PasswordBearer";
              value: {}
            }
          ]
        }
      ],
      tags: [
        "审批",
        "审批模版"
      ]
    };
  }

}

message TicketRequest {
  string code = 1;
}


message JsSDKSignatureRequest {
  string code = 1; // 企业微信的应用编码
  string url = 2; // 当前网页的URL，不包含#及其后面部分
}

message JsSDKSignatureResponse {
  string signature = 1; // 签名
  string nonce_str = 2; // 随机字符串
  int64 timestamp = 3; // 时间戳
  string url = 4; // 当前网页的URL，不包含#及其后面部分
}



message RegisterInfoResponse {
  string corpId = 1; // 企业微信的CorpID
  optional int64 agentId = 2; // 企业微信的应用ID
  string suiteId = 3; // 企业微信的套件ID
}

// URL验证请求
message VerifyURLRequest {
  string msg_signature = 1; // 企业微信加密签名
  string timestamp = 2;     // 时间戳
  string nonce = 3;         // 随机数
  string echostr = 4;       // 加密的字符串
}


// URL验证响应
message VerifyURLResponse {
  string echostr = 1;       // 解密后的消息内容
}

// 接收消息请求
message ReceiveMessageRequest {
  string msg_signature = 1; // 企业微信加密签名
  string timestamp = 2;     // 时间戳
  string nonce = 3;         // 随机数
  string to_user_name = 4;  // 企业微信的CorpID
  string agent_id = 5;      // 接收的应用id
  string encrypt = 6;       // 消息结构体加密后的字符串
}

// 接收消息响应
message ReceiveMessageResponse {
  string encrypt = 1;        // 经过加密的消息结构体
  string msg_signature = 2;  // 消息签名
  string timestamp = 3;      // 时间戳
  string nonce = 4;         // 随机数
}

// 获取回调IP响应
message GetCallbackIPResponse {
  int32 errcode = 1;        // 错误码
  string errmsg = 2;        // 错误信息
  repeated string ip_list = 3; // 企业微信回调的IP段列表
}


message JsApiTicketResponse {
  string ticket = 1;
}



message LoginWithWorkWechatRequest {
  string code = 1;
  string platform = 2;
}

message WorkWechatLoginUrlResponse {
  string url = 1;
}

message WorkWechatLoginUrlRequest {
  string redirect_uri = 1;
}

message UnBindWxworkRequest {
  optional string platform = 3;
}

message BindWxworkRequest {
  string username = 1;
  string password = 2;
  string bind_code = 3 [json_name = "bindCode"];
  optional string platform = 4;
}

message LoginResponse {
  string id = 1;
  string user_id = 2 [json_name = "userId"];
  string username = 3;
  string nickname = 4;
  string token = 5 [json_name = "token"];
  bool hasPhone = 6;
  bool is_old_password = 7 [json_name = "isOldPassword"];
}