package data

import (
	"context"
	"crypto/rand"
	"crypto/sha1"
	"fmt"
	systemv1 "kratos-mono-demo/gen/api/system/v1"
	v1 "kratos-mono-demo/gen/api/workwx/v1"
	conf "kratos-mono-demo/gen/config/conf/v1"
	"kratos-mono-demo/internal/app/workwx/biz"
	"kratos-mono-demo/internal/data"
	"kratos-mono-demo/internal/data/ent"
	"kratos-mono-demo/internal/data/ent/workwxapp"
	"kratos-mono-demo/internal/pkg/go-utils/clogger"
	"kratos-mono-demo/internal/pkg/workwechat"
	"math/big"
	mathrand "math/rand"
	"net/url"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"github.com/xen0n/go-workwx/v2"
)

var _ biz.WorkwxRepo = (*WorkwxRepo)(nil)

type WorkwxRepo struct {
	log    *log.Helper
	config *conf.Config
	data   *data.Data
}

func NewWorkwxRepo(config *conf.Config, logger log.Logger, data *data.Data) biz.WorkwxRepo {
	return &WorkwxRepo{
		log:    log.NewHelper(logger),
		config: config,
		data:   data,
	}
}

func (r *WorkwxRepo) getEntClient(ctx context.Context) *ent.Client {
	tx := ent.TxFromContext(ctx)
	if tx != nil {
		return tx.Client()
	}
	return r.data.EntClient.Client()
}

func (r *WorkwxRepo) findWorkWxApp(ctx context.Context, code string) (*ent.WorkWxApp, error) {
	query := r.getEntClient(ctx).WorkWxApp.Query()
	if code != "" {
		query = query.Where(workwxapp.CodeEQ(code))
	}
	app, err := query.First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			if code == "" {
				return nil, fmt.Errorf("workwx app not configured: %w", err)
			}
			return nil, fmt.Errorf("workwx app(code=%s) not found: %w", code, err)
		}
		return nil, err
	}
	return app, nil
}

func (r *WorkwxRepo) ensureAppCredentials(app *ent.WorkWxApp) error {
	if app.CorpID == nil || *app.CorpID == "" {
		return fmt.Errorf("workwx app(code=%s) missing corp_id", app.Code)
	}
	if app.CorpSecret == nil || *app.CorpSecret == "" {
		return fmt.Errorf("workwx app(code=%s) missing corp_secret", app.Code)
	}
	if app.AgentID == nil {
		return fmt.Errorf("workwx app(code=%s) missing agent_id", app.Code)
	}
	return nil
}

func stringValue(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}

func (r *WorkwxRepo) LoginWithWorkWechat(ctx context.Context, code string) (*systemv1.LoginResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (r *WorkwxRepo) GetWorkWechatLoginUrl(ctx context.Context, redirectUri string) (string, error) {
	authHost := r.config.Oauth.WorkWechat.AuthHost
	state := uuid.New().String()
	state = strings.ReplaceAll(state, "-", "")
	return fmt.Sprintf("%s/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_base&state=%s&agentid=%d#wechat_redirect", authHost,
		r.config.Oauth.WorkWechat.CorpId, url.QueryEscape(redirectUri), state, r.config.Oauth.WorkWechat.AgentId), nil

}

func (r *WorkwxRepo) BindWxwork(ctx context.Context, req *v1.BindWxworkRequest) (*systemv1.LoginResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (r *WorkwxRepo) UnbindWxwork(ctx context.Context, req *v1.UnBindWxworkRequest) error {
	//TODO implement me
	panic("implement me")
}

func (r *WorkwxRepo) VerifyURL(ctx context.Context, req *v1.VerifyURLRequest) (string, error) {
	wxCrypt := workwechat.NewWXBizMsgCrypt(r.config.Oauth.WorkWechat.MsgCallbackToken, r.config.Oauth.WorkWechat.MsgCallbackAesKey, r.config.Oauth.WorkWechat.CorpId, workwechat.XmlType)

	// 调用 VerifyURL 方法进行验证
	echostr, cryptErr := wxCrypt.VerifyURL(req.MsgSignature, req.Timestamp, req.Nonce, req.Echostr)
	if cryptErr != nil {
		r.log.Errorf("VerifyURL error: code=%d, msg=%s", cryptErr.ErrCode, cryptErr.ErrMsg)
		return "", fmt.Errorf("verify URL failed: code=%d, msg=%s", cryptErr.ErrCode, cryptErr.ErrMsg)
	}

	return string(echostr), nil
}

func (r *WorkwxRepo) RegisterInfo(ctx context.Context, req *v1.TicketRequest) (*v1.RegisterInfoResponse, error) {
	app, err := r.findWorkWxApp(ctx, req.GetCode())
	if err != nil {
		return nil, err
	}

	resp := &v1.RegisterInfoResponse{
		CorpId:  stringValue(app.CorpID),
		SuiteId: stringValue(app.SuiteID),
	}
	if app.AgentID != nil {
		resp.AgentId = app.AgentID
	}
	return resp, nil
}

func (r *WorkwxRepo) ReceiveMessage(ctx context.Context, req *v1.ReceiveMessageRequest) (*v1.ReceiveMessageResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (r *WorkwxRepo) GetCallbackIP(ctx context.Context) (*v1.GetCallbackIPResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (r *WorkwxRepo) CorpJsApiTicket(ctx context.Context, req *v1.TicketRequest) (*v1.JsApiTicketResponse, error) {
	clogger.Println("CorpJsApiTicket enter")
	app, err := r.findWorkWxApp(ctx, req.GetCode())
	if err != nil {
		clogger.Println("findWorkWxApp error: ", err)
		return nil, err
	}
	if err := r.ensureAppCredentials(app); err != nil {
		clogger.Println("ensureAppCredentials error: ", err)
		return nil, err
	}

	// redisKey := fmt.Sprintf("workwx:corp_jsapi_ticket:%s", app.Code)
	// if ticket, err := r.data.RedisClient.Get(ctx, redisKey).Result(); err == nil {
	// 	return &v1.JsApiTicketResponse{Ticket: ticket}, nil
	// }

	wx := workwx.New(*app.CorpID)
	client := wx.WithApp(*app.CorpSecret, *app.AgentID)
	ret, err := client.GetJSAPITicket()
	if err != nil {
		clogger.Println("GetJSAPITicket error: ", err)
		return nil, err
	}

	clogger.Println("corp jsapi ticket: ", ret)

	// if err := r.data.RedisClient.Set(ctx, redisKey, ret, 7000*time.Second).Err(); err != nil {
	// 	r.log.Warnf("Failed to cache corp jsapi ticket: %v", err)
	// }

	return &v1.JsApiTicketResponse{Ticket: ret}, nil
}

func (r *WorkwxRepo) AgentJsApiTicket(ctx context.Context, req *v1.TicketRequest) (*v1.JsApiTicketResponse, error) {
	clogger.Println("AgentJsApiTicket enter")
	app, err := r.findWorkWxApp(ctx, req.GetCode())
	if err != nil {
		return nil, err
	}
	if err := r.ensureAppCredentials(app); err != nil {
		return nil, err
	}

	// redisKey := fmt.Sprintf("workwx:agent_jsapi_ticket:%s", app.Code)
	// if ticket, err := r.data.RedisClient.Get(ctx, redisKey).Result(); err == nil {
	// 	return &v1.JsApiTicketResponse{Ticket: ticket}, nil
	// }

	wx := workwx.New(*app.CorpID)
	client := wx.WithApp(*app.CorpSecret, *app.AgentID)
	ret, err := client.GetJSAPITicketAgentConfig()
	if err != nil {
		return nil, err
	}

	clogger.Println("agent jsapi ticket: ", ret)

	// if err := r.data.RedisClient.Set(ctx, redisKey, ret, 7000*time.Second).Err(); err != nil {
	// 	r.log.Warnf("Failed to cache agent jsapi ticket: %v", err)
	// }

	return &v1.JsApiTicketResponse{Ticket: ret}, nil
}

func (r *WorkwxRepo) FindWorkwxApp(ctx context.Context, code string) (*ent.WorkWxApp, error) {
	app, err := r.findWorkWxApp(ctx, code)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return app, nil
}

func (r *WorkwxRepo) UpdateSuiteTicket(ctx context.Context, suiteId string, ticket string) {
	if suiteId == "" {
		return
	}
	_, err := r.getEntClient(ctx).WorkWxApp.
		Update().
		Where(workwxapp.SuiteIDEQ(suiteId)).
		SetSuiteTicket(ticket).
		Save(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			r.log.Warnf("UpdateSuiteTicket skip: suite_id=%s not found", suiteId)
			return
		}
		r.log.Errorf("UpdateSuiteTicket failed: %v", err)
	}
}

func (r *WorkwxRepo) JsSDKSignature(ctx context.Context, req *v1.JsSDKSignatureRequest) (*v1.JsSDKSignatureResponse, error) {
	clogger.Println("JsSDKSignature enter")
	agentTicket, err := r.AgentJsApiTicket(ctx, &v1.TicketRequest{
		Code: req.Code,
	})
	if err != nil {
		return nil, err
	}

	// 生成随机字符串
	nonceStr := generateNonceStr(16)
	// 生成时间戳(秒)
	timestamp := time.Now().Unix()

	// 按照文档要求拼接字符串
	// jsapi_ticket=JSAPI_TICKET&noncestr=NONCESTR&timestamp=TIMESTAMP&url=URL
	str := fmt.Sprintf("jsapi_ticket=%s&noncestr=%s&timestamp=%d&url=%s",
		agentTicket.Ticket, nonceStr, timestamp, req.Url)

	// 计算SHA-1签名
	h := sha1.New()
	h.Write([]byte(str))
	signature := fmt.Sprintf("%x", h.Sum(nil))

	// 返回完整响应
	return &v1.JsSDKSignatureResponse{
		Signature: signature,
		NonceStr:  nonceStr,
		Timestamp: timestamp,
		Url:       req.Url,
	}, nil
}

// JsSDKConfigSignature 生成JS-SDK配置签名
func (r *WorkwxRepo) JsSDKConfigSignature(ctx context.Context, req *v1.JsSDKSignatureRequest) (*v1.JsSDKSignatureResponse, error) {
	clogger.Println("JsSDKConfigSignature enter")
	agentTicket, err := r.CorpJsApiTicket(ctx, &v1.TicketRequest{
		Code: req.Code,
	})
	if err != nil {
		return nil, err
	}

	// 生成随机字符串
	nonceStr := generateNonceStr(16)
	// 生成时间戳(秒)
	timestamp := time.Now().Unix()

	// 按照文档要求拼接字符串
	// jsapi_ticket=JSAPI_TICKET&noncestr=NONCESTR&timestamp=TIMESTAMP&url=URL
	str := fmt.Sprintf("jsapi_ticket=%s&noncestr=%s&timestamp=%d&url=%s",
		agentTicket.Ticket, nonceStr, timestamp, req.Url)

	// 计算SHA-1签名
	h := sha1.New()
	h.Write([]byte(str))
	signature := fmt.Sprintf("%x", h.Sum(nil))

	// 返回完整响应
	return &v1.JsSDKSignatureResponse{
		Signature: signature,
		NonceStr:  nonceStr,
		Timestamp: timestamp,
		Url:       req.Url,
	}, nil
}

// generateNonceStr 生成指定长度的随机字符串
func generateNonceStr(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		// 使用crypto/rand生成安全的随机数
		n, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			// 如果出错则使用时间作为随机种子
			r := mathrand.New(mathrand.NewSource(time.Now().UnixNano()))
			b[i] = charset[r.Intn(len(charset))]
			continue
		}
		b[i] = charset[n.Int64()]
	}
	return string(b)
}
